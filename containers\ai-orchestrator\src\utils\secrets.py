"""
Docker Secrets Utility for AI Coding Agent.

This module provides utilities for reading Docker Secrets from mounted files,
with fallback to environment variables for development environments.

Author: AI Coding Agent Team
Version: 1.0.0
"""

import os
import logging
from typing import Optional, Dict, Any
from pathlib import Path

logger = logging.getLogger(__name__)

# Docker secrets are mounted at /run/secrets/ by default
SECRETS_PATH = Path("/run/secrets")


def read_secret(secret_name: str, env_var_name: Optional[str] = None, default: Optional[str] = None) -> Optional[str]:
    """
    Read a secret from Docker Secrets file with fallback to environment variable.

    Args:
        secret_name: Name of the secret file (without path)
        env_var_name: Environment variable name to fall back to
        default: Default value if neither secret file nor env var exists

    Returns:
        Secret value as string, or None if not found and no default

    Raises:
        ValueError: If secret is required but not found
    """
    # Try reading from Docker Secrets file first
    secret_file = SECRETS_PATH / secret_name

    if secret_file.exists() and secret_file.is_file():
        try:
            with open(secret_file, 'r', encoding='utf-8') as f:
                secret_value = f.read().strip()

            # Skip empty files or files with just comments
            if secret_value and not secret_value.startswith('#'):
                logger.debug(f"Successfully read secret from file: {secret_name}")
                return secret_value
            else:
                logger.warning(f"Secret file {secret_name} is empty or contains only comments")

        except (OSError, IOError) as e:
            logger.error(f"Failed to read secret file {secret_name}: {e}")

    # Fallback to environment variable
    if env_var_name:
        env_value = os.getenv(env_var_name)
        if env_value:
            logger.debug(f"Using environment variable {env_var_name} as fallback for secret {secret_name}")
            return env_value

    # Use default if provided
    if default is not None:
        logger.debug(f"Using default value for secret {secret_name}")
        return default

    # Not found
    logger.warning(f"Secret {secret_name} not found in file or environment variable {env_var_name}")
    return None


def read_secret_required(secret_name: str, env_var_name: Optional[str] = None) -> str:
    """
    Read a required secret, raising an error if not found.

    Args:
        secret_name: Name of the secret file
        env_var_name: Environment variable name to fall back to

    Returns:
        Secret value as string

    Raises:
        ValueError: If secret is not found
    """
    secret_value = read_secret(secret_name, env_var_name)

    if secret_value is None:
        raise ValueError(
            f"Required secret '{secret_name}' not found. "
            f"Please ensure the secret file exists at {SECRETS_PATH}/{secret_name} "
            f"or set environment variable {env_var_name}"
        )

    return secret_value


def get_database_url() -> str:
    """
    Construct database URL using secrets for password.

    Returns:
        Complete database URL with password from secrets
    """
    password = read_secret_required("postgres_password", "POSTGRES_PASSWORD")

    # Get other database connection parameters
    host = os.getenv("DATABASE_HOST", "postgresql")
    port = os.getenv("DATABASE_PORT", "5432")
    name = os.getenv("DATABASE_NAME", "ai_coding_agent")
    user = os.getenv("DATABASE_USER", "postgres")

    return f"postgresql://{user}:{password}@{host}:{port}/{name}"


def get_supabase_config() -> Dict[str, str]:
    """
    Get Supabase configuration from secrets.

    Returns:
        Dictionary with Supabase configuration
    """
    return {
        "url": read_secret_required("supabase_url", "SUPABASE_URL"),
        "anon_key": read_secret_required("supabase_key", "SUPABASE_KEY"),
        "service_key": read_secret_required("supabase_service_key", "SUPABASE_SERVICE_KEY"),
        "jwt_secret": read_secret_required("jwt_secret", "JWT_SECRET"),
    }


def get_auth_config() -> Dict[str, Any]:
    """
    Get authentication configuration from secrets and environment.

    Returns:
        Dictionary with authentication configuration
    """
    config = get_supabase_config()

    # Add other auth-related configuration
    config.update({
        "jwt_algorithm": os.getenv("JWT_ALGORITHM", "HS256"),
        "access_token_expire_minutes": int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30")),
        "refresh_token_expire_days": int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7")),
    })

    return config


def validate_secrets() -> Dict[str, bool]:
    """
    Validate that all required secrets are available.

    Returns:
        Dictionary mapping secret names to availability status
    """
    required_secrets = [
        ("postgres_password", "POSTGRES_PASSWORD"),
        ("jwt_secret", "JWT_SECRET"),
        ("supabase_url", "SUPABASE_URL"),
        ("supabase_key", "SUPABASE_KEY"),
        ("supabase_service_key", "SUPABASE_SERVICE_KEY"),
    ]

    validation_results = {}

    for secret_name, env_var in required_secrets:
        try:
            value = read_secret_required(secret_name, env_var)
            validation_results[secret_name] = bool(value)
        except ValueError:
            validation_results[secret_name] = False

    return validation_results


def get_secrets_status() -> Dict[str, Any]:
    """
    Get comprehensive status of secrets configuration.

    Returns:
        Dictionary with secrets status information
    """
    validation_results = validate_secrets()

    return {
        "secrets_available": validation_results,
        "all_secrets_valid": all(validation_results.values()),
        "secrets_path_exists": SECRETS_PATH.exists(),
        "secrets_path": str(SECRETS_PATH),
        "missing_secrets": [name for name, valid in validation_results.items() if not valid],
    }


# Pre-validate secrets on module import for early error detection
def _validate_on_import():
    """Validate secrets on module import to catch configuration issues early."""
    try:
        status = get_secrets_status()
        if not status["all_secrets_valid"]:
            missing = ", ".join(status["missing_secrets"])
            logger.warning(f"Some required secrets are missing: {missing}")
        else:
            logger.info("All required secrets are available")
    except Exception as e:
        logger.error(f"Failed to validate secrets on import: {e}")


# Run validation on import
_validate_on_import()