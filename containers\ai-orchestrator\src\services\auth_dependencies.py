# Project: AI Coding Agent
# Purpose: Authentication dependencies for FastAPI
# Author: AI Coding Agent Team

"""
Authentication Dependencies for AI Coding Agent.

This module provides FastAPI dependencies for authentication and authorization,
specifically designed for Supabase Auth integration with JWT token validation.
"""

import logging
import os
from typing import Optional
from datetime import datetime, timezone
from uuid import UUID

# FastAPI and HTTP imports
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
import jwt
import httpx

# Internal imports
from ..schemas.user_schemas import SupabaseUser
from src.core.config import settings

logger = logging.getLogger(__name__)

# Security scheme for JWT Bearer tokens
security = HTTPBearer()

# Supabase configuration via centralized settings
SUPABASE_URL = str(settings.SUPABASE_URL)
# Prefer Pydantic settings field; fallback to legacy env var name for compatibility
SUPABASE_ANON_KEY = settings.SUPABASE_ANON_KEY or os.getenv("SUPABASE_KEY")
SUPABASE_JWT_SECRET = settings.JWT_SECRET


class AuthenticationError(Exception):
    """Base authentication error."""
    pass


class TokenExpiredError(AuthenticationError):
    """Token has expired."""
    pass


class InvalidTokenError(AuthenticationError):
    """Token is invalid or malformed."""
    pass


class InsufficientPermissionsError(AuthenticationError):
    """User doesn't have required permissions."""
    pass


async def get_current_user_from_supabase(
    credentials: HTTPAuthorizationCredentials = Depends(security)
) -> SupabaseUser:
    """
    Extract and validate current user from Supabase JWT token.

    This dependency:
    1. Extracts JWT token from Authorization header
    2. Validates token signature with Supabase secret
    3. Parses user data from token payload
    4. Returns SupabaseUser instance

    Args:
        credentials: HTTP Bearer token from Authorization header

    Returns:
        SupabaseUser: Validated user data from token

    Raises:
        HTTPException: If token is invalid, expired, or malformed
    """
    try:
        token = credentials.credentials
        logger.debug(f"Validating JWT token: {token[:20]}...")

        # Decode and validate JWT token
        try:
            payload = jwt.decode(
                token,
                SUPABASE_JWT_SECRET,
                algorithms=["HS256"],
                audience="authenticated"
            )
        except jwt.ExpiredSignatureError:
            logger.warning("JWT token has expired")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token has expired",
                headers={"WWW-Authenticate": "Bearer"},
            )
        except jwt.InvalidTokenError as e:
            logger.warning(f"Invalid JWT token: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract user data from JWT payload
        user_id = payload.get("sub")
        email = payload.get("email")

        if not user_id or not email:
            logger.error("JWT token missing required user data")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid token payload",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Parse additional user metadata
        user_metadata = payload.get("user_metadata", {})
        app_metadata = payload.get("app_metadata", {})

        # Convert timestamps
        iat = payload.get("iat")  # Issued at
        exp = payload.get("exp")  # Expires at
        email_confirmed_at = payload.get("email_confirmed_at")
        last_sign_in_at = payload.get("last_sign_in_at")

        # Create SupabaseUser instance
        supabase_user = SupabaseUser(
            id=UUID(user_id),
            email=email,
            email_confirmed_at=datetime.fromtimestamp(email_confirmed_at, timezone.utc) if email_confirmed_at else None,
            created_at=datetime.fromtimestamp(iat, timezone.utc) if iat else datetime.now(timezone.utc),
            updated_at=datetime.now(timezone.utc),
            last_sign_in_at=datetime.fromtimestamp(last_sign_in_at, timezone.utc) if last_sign_in_at else None,
            user_metadata=user_metadata,
            app_metadata=app_metadata
        )

        logger.debug(f"Successfully authenticated user: {email}")
        return supabase_user

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in authentication: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Authentication service error"
        )


async def get_optional_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(HTTPBearer(auto_error=False))
) -> Optional[SupabaseUser]:
    """
    Optional authentication dependency that returns None if no token provided.

    Useful for endpoints that work for both authenticated and anonymous users.

    Args:
        credentials: Optional HTTP Bearer token

    Returns:
        Optional[SupabaseUser]: User data if authenticated, None otherwise
    """
    if not credentials:
        return None

    try:
        return await get_current_user_from_supabase(credentials)
    except HTTPException:
        # Return None instead of raising exception for optional auth
        return None


async def require_admin_user(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase)
) -> SupabaseUser:
    """
    Dependency that ensures current user has admin privileges.

    Checks for admin role in either app_metadata or user_metadata.

    Args:
        current_user: Current authenticated user from Supabase

    Returns:
        SupabaseUser: Verified admin user

    Raises:
        HTTPException: If user is not an admin
    """
    # Check admin status in app_metadata (preferred)
    is_admin = (
        current_user.app_metadata.get("role") == "admin" or
        current_user.app_metadata.get("admin") is True or
        current_user.user_metadata.get("role") == "admin" or
        current_user.user_metadata.get("admin") is True
    )

    if not is_admin:
        logger.warning(f"Non-admin user {current_user.email} attempted admin operation")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Administrative privileges required"
        )

    logger.debug(f"Admin access granted to: {current_user.email}")
    return current_user


async def require_verified_email(
    current_user: SupabaseUser = Depends(get_current_user_from_supabase)
) -> SupabaseUser:
    """
    Dependency that ensures current user has verified their email.

    Args:
        current_user: Current authenticated user

    Returns:
        SupabaseUser: User with verified email

    Raises:
        HTTPException: If email is not verified
    """
    if not current_user.email_confirmed_at:
        logger.warning(f"Unverified user {current_user.email} attempted verified-only operation")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Email verification required"
        )

    return current_user


async def require_user_permissions(permissions: list[str]):
    """
    Factory function to create permission-checking dependencies.

    Args:
        permissions: List of required permissions

    Returns:
        Dependency function that checks permissions
    """
    async def permission_checker(
        current_user: SupabaseUser = Depends(get_current_user_from_supabase)
    ) -> SupabaseUser:
        # Get user permissions from metadata
        user_permissions = (
            current_user.app_metadata.get("permissions", []) +
            current_user.user_metadata.get("permissions", [])
        )

        # Check if user has all required permissions
        missing_permissions = [p for p in permissions if p not in user_permissions]

        if missing_permissions:
            logger.warning(
                f"User {current_user.email} missing permissions: {missing_permissions}"
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Missing required permissions: {', '.join(missing_permissions)}"
            )

        return current_user

    return permission_checker


# ==================================================================================
# DEVELOPMENT/TESTING DEPENDENCIES
# ==================================================================================

async def get_mock_current_user() -> SupabaseUser:
    """
    Mock authentication dependency for development/testing.

    Returns a mock user for development environments when ENVIRONMENT=development.
    Should never be used in production!

    Returns:
        SupabaseUser: Mock user data
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment != "development":
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Mock authentication only available in development"
        )

    from uuid import uuid4

    return SupabaseUser(
        id=uuid4(),
        email="<EMAIL>",
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc),
        email_confirmed_at=datetime.now(timezone.utc),
        user_metadata={
            "full_name": "Development User",
            "username": "devuser"
        },
        app_metadata={"role": "admin"}  # Give dev user admin rights
    )


async def get_mock_admin_user() -> SupabaseUser:
    """
    Mock admin user for development/testing.

    Returns:
        SupabaseUser: Mock admin user
    """
    user = await get_mock_current_user()
    user.app_metadata["role"] = "admin"
    user.email = "<EMAIL>"
    user.user_metadata["username"] = "adminuser"
    user.user_metadata["full_name"] = "Admin User"
    return user


# ==================================================================================
# ENVIRONMENT-AWARE DEPENDENCY SELECTION
# ==================================================================================

def get_auth_dependency():
    """
    Get the appropriate authentication dependency based on environment.

    Returns:
        Callable: Authentication dependency function
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment == "development" and os.getenv("USE_MOCK_AUTH", "false").lower() == "true":
        logger.warning("Using mock authentication in development mode!")
        return get_mock_current_user

    return get_current_user_from_supabase


def get_admin_dependency():
    """
    Get the appropriate admin authentication dependency based on environment.

    Returns:
        Callable: Admin authentication dependency function
    """
    environment = os.getenv("ENVIRONMENT", "production").lower()

    if environment == "development" and os.getenv("USE_MOCK_AUTH", "false").lower() == "true":
        logger.warning("Using mock admin authentication in development mode!")
        return get_mock_admin_user

    return require_admin_user


# ==================================================================================
# TOKEN UTILITIES
# ==================================================================================

def create_access_token(user_data: dict, expires_delta: Optional[int] = None) -> str:
    """
    Create a JWT access token (for testing purposes).

    Note: In production, tokens should be created by Supabase Auth service.
    This is mainly for testing and development.

    Args:
        user_data: User data to encode in token
        expires_delta: Token expiration in seconds

    Returns:
        str: JWT token
    """
    if not SUPABASE_JWT_SECRET:
        raise ValueError("JWT secret not configured")

    now = datetime.now(timezone.utc)
    expire = now.timestamp() + (expires_delta or 3600)  # Default 1 hour

    payload = {
        "sub": str(user_data.get("id")),
        "email": user_data.get("email"),
        "iat": now.timestamp(),
        "exp": expire,
        "aud": "authenticated",
        "user_metadata": user_data.get("user_metadata", {}),
        "app_metadata": user_data.get("app_metadata", {})
    }

    return jwt.encode(payload, SUPABASE_JWT_SECRET, algorithm="HS256")


async def validate_supabase_connection() -> dict:
    """
    Validate connection to Supabase Auth service.

    Returns:
        dict: Connection status and metadata
    """
    try:
        if not SUPABASE_URL or not SUPABASE_ANON_KEY:
            return {
                "status": "unconfigured",
                "message": "Supabase credentials not configured"
            }

        # Test connection to Supabase Auth
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{SUPABASE_URL}/auth/v1/settings",
                headers={
                    "apikey": SUPABASE_ANON_KEY,
                    "Authorization": f"Bearer {SUPABASE_ANON_KEY}"
                },
                timeout=5.0
            )

        if response.status_code == 200:
            return {
                "status": "connected",
                "message": "Supabase Auth connection successful",
                "data": response.json()
            }
        else:
            return {
                "status": "error",
                "message": f"Supabase Auth returned status {response.status_code}"
            }

    except Exception as e:
        logger.error(f"Supabase connection test failed: {str(e)}")
        return {
            "status": "error",
            "message": f"Connection failed: {str(e)}"
        }