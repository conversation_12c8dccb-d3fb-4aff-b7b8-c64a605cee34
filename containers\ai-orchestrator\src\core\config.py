"""
Centralized Configuration Management for AI Coding Agent.

This module provides a single source of truth for all configuration values
using Pydantic settings with proper type validation, environment variable
parsing, and development/production environment support.

Author: AI Coding Agent
Version: 1.0.0
"""

import os
from typing import List, Optional, Union
from enum import Enum
from pathlib import Path

from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, RedisDsn, validator, Field, AnyHttpUrl


class Environment(str, Enum):
    """Application environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"
    TESTING = "testing"


class EmbeddingProvider(str, Enum):
    """Supported embedding providers."""
    SENTENCE_TRANSFORMERS = "sentence_transformers"
    OPENAI = "openai"
    HUGGINGFACE = "huggingface"


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OLLAMA = "ollama"
    OPENROUTER = "openrouter"
    OPENAI = "openai"
    ANTHROPIC = "anthropic"


class Settings(BaseSettings):
    """
    Centralized application settings.

    All configuration values are defined here with proper types,
    validation, and environment variable mapping.
    """

    # =====================================================================================
    # CORE APPLICATION SETTINGS
    # =====================================================================================

    APP_NAME: str = Field(default="AI Coding Agent Orchestrator", description="Application name")
    APP_VERSION: str = Field(default="1.0.0", description="Application version")
    ENVIRONMENT: Environment = Field(default=Environment.DEVELOPMENT, description="Application environment")
    DEBUG: bool = Field(default=False, description="Debug mode flag")

    # API Configuration
    API_V1_PREFIX: str = Field(default="/api/v1", description="API version 1 prefix")
    DOCS_URL: str = Field(default="/api/v1/docs", description="API documentation URL")
    REDOC_URL: str = Field(default="/api/v1/redoc", description="ReDoc documentation URL")

    # CORS Configuration
    CORS_ORIGINS: List[str] = Field(
        default=["http://localhost:3000", "http://localhost:8080"],
        description="Allowed CORS origins"
    )
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True, description="Allow CORS credentials")
    CORS_ALLOW_METHODS: List[str] = Field(default=["*"], description="Allowed CORS methods")
    CORS_ALLOW_HEADERS: List[str] = Field(default=["*"], description="Allowed CORS headers")

    @validator('CORS_ORIGINS', pre=True)
    def parse_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        """Parse CORS origins from comma-separated string or list."""
        if isinstance(v, str):
            return [origin.strip() for origin in v.split(",")]
        return v

    # =====================================================================================
    # DATABASE CONFIGURATION
    # =====================================================================================

    DATABASE_URL: PostgresDsn = Field(..., description="PostgreSQL database URL with pgvector")
    DATABASE_POOL_SIZE: int = Field(default=20, description="Database connection pool size")
    DATABASE_POOL_OVERFLOW: int = Field(default=0, description="Database pool overflow")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout in seconds")
    DATABASE_ECHO: bool = Field(default=False, description="Echo SQL queries (debug mode)")

    # =====================================================================================
    # REDIS CONFIGURATION
    # =====================================================================================

    REDIS_URL: RedisDsn = Field(..., description="Redis server URL for caching and sessions")
    REDIS_MAX_CONNECTIONS: int = Field(default=20, description="Redis connection pool size")
    REDIS_RETRY_ON_TIMEOUT: bool = Field(default=True, description="Retry Redis operations on timeout")
    REDIS_SOCKET_KEEPALIVE: bool = Field(default=True, description="Enable Redis socket keepalive")
    REDIS_SOCKET_KEEPALIVE_OPTIONS: dict = Field(
        default_factory=lambda: {},
        description="Redis socket keepalive options"
    )

    # =====================================================================================
    # SUPABASE CONFIGURATION
    # =====================================================================================

    USE_SUPABASE: bool = Field(default=False, description="Enable Supabase integration")
    SUPABASE_URL: Optional[AnyHttpUrl] = Field(default=None, description="Supabase project URL")
    SUPABASE_ANON_KEY: Optional[str] = Field(default=None, description="Supabase anonymous key")
    SUPABASE_SERVICE_KEY: Optional[str] = Field(default=None, description="Supabase service role key")
    SUPABASE_DB_URL: Optional[PostgresDsn] = Field(default=None, description="Supabase database URL")

    # Supabase Connection Pool Settings
    SUPABASE_MAX_CONNECTIONS: int = Field(default=20, description="Supabase max connections")
    SUPABASE_MIN_CONNECTIONS: int = Field(default=5, description="Supabase min connections")
    SUPABASE_CONNECTION_TIMEOUT: float = Field(default=30.0, description="Supabase connection timeout")
    SUPABASE_COMMAND_TIMEOUT: float = Field(default=60.0, description="Supabase command timeout")
    SUPABASE_RETRY_ATTEMPTS: int = Field(default=3, description="Supabase retry attempts")
    SUPABASE_RETRY_DELAY: float = Field(default=1.0, description="Supabase retry delay")
    SUPABASE_ENABLE_SSL: bool = Field(default=False, description="Enable SSL for Supabase")

    @validator('SUPABASE_URL', 'SUPABASE_ANON_KEY', 'SUPABASE_SERVICE_KEY', 'SUPABASE_DB_URL')
    def validate_supabase_config(cls, v, values):
        """Validate Supabase configuration consistency."""
        use_supabase = values.get('USE_SUPABASE', False)
        if use_supabase and v is None:
            raise ValueError(f"Supabase configuration required when USE_SUPABASE=true")
        return v

    # =====================================================================================
    # AUTHENTICATION & SECURITY
    # =====================================================================================

    JWT_SECRET: str = Field(..., description="JWT signing secret key")
    JWT_ALGORITHM: str = Field(default="HS256", description="JWT signing algorithm")
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(default=30, description="Access token expiration in minutes")
    JWT_REFRESH_TOKEN_EXPIRE_DAYS: int = Field(default=7, description="Refresh token expiration in days")

    # Password Policy
    PASSWORD_MIN_LENGTH: int = Field(default=8, description="Minimum password length")
    REQUIRE_EMAIL_VERIFICATION: bool = Field(default=True, description="Require email verification")
    ALLOW_SIGNUP: bool = Field(default=True, description="Allow user registration")
    MAX_LOGIN_ATTEMPTS: int = Field(default=5, description="Maximum login attempts")
    LOGIN_ATTEMPT_WINDOW_MINUTES: int = Field(default=15, description="Login attempt window in minutes")

    # OAuth Configuration
    GITHUB_CLIENT_ID: Optional[str] = Field(default=None, description="GitHub OAuth client ID")
    GITHUB_CLIENT_SECRET: Optional[str] = Field(default=None, description="GitHub OAuth client secret")
    GITHUB_REDIRECT_URI: str = Field(
        default="http://localhost:8000/auth/github/callback",
        description="GitHub OAuth redirect URI"
    )

    # =====================================================================================
    # LLM PROVIDER CONFIGURATION
    # =====================================================================================

    # Ollama (Local LLM)
    OLLAMA_BASE_URL: AnyHttpUrl = Field(
        default="http://host.docker.internal:11434",
        description="Ollama server base URL"
    )
    OLLAMA_MODEL: str = Field(default="llama3.1", description="Default Ollama model")
    OLLAMA_TIMEOUT: int = Field(default=300, description="Ollama request timeout in seconds")

    # OpenRouter (Cloud LLM)
    OPENROUTER_API_KEY: Optional[str] = Field(default=None, description="OpenRouter API key")
    OPENROUTER_BASE_URL: AnyHttpUrl = Field(
        default="https://openrouter.ai/api/v1",
        description="OpenRouter API base URL"
    )
    OPENROUTER_MODEL: str = Field(
        default="anthropic/claude-3.5-sonnet",
        description="Default OpenRouter model"
    )

    # OpenAI
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    OPENAI_BASE_URL: AnyHttpUrl = Field(
        default="https://api.openai.com/v1",
        description="OpenAI API base URL"
    )
    OPENAI_MODEL: str = Field(default="gpt-4", description="Default OpenAI model")

    # Anthropic
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    ANTHROPIC_BASE_URL: AnyHttpUrl = Field(
        default="https://api.anthropic.com",
        description="Anthropic API base URL"
    )
    ANTHROPIC_MODEL: str = Field(
        default="claude-3-5-sonnet-20241022",
        description="Default Anthropic model"
    )

    # LLM Configuration
    DEFAULT_LOCAL_PROVIDER: LLMProvider = Field(
        default=LLMProvider.OLLAMA,
        description="Default local LLM provider"
    )
    DEFAULT_CLOUD_PROVIDER: LLMProvider = Field(
        default=LLMProvider.OPENROUTER,
        description="Default cloud LLM provider"
    )
    ENABLE_CLOUD_FALLBACK: bool = Field(
        default=True,
        description="Enable fallback to cloud providers"
    )
    LLM_REQUEST_TIMEOUT: int = Field(default=300, description="LLM request timeout in seconds")
    LLM_MAX_RETRIES: int = Field(default=3, description="Maximum LLM request retries")

    # =====================================================================================
    # VECTOR & EMBEDDING CONFIGURATION
    # =====================================================================================

    EMBEDDING_PROVIDER: EmbeddingProvider = Field(
        default=EmbeddingProvider.SENTENCE_TRANSFORMERS,
        description="Embedding provider to use"
    )
    EMBEDDING_MODEL: str = Field(
        default="all-MiniLM-L6-v2",
        description="Embedding model name"
    )
    EMBEDDING_DIMENSION: int = Field(default=384, description="Embedding vector dimension")
    CHUNK_SIZE: int = Field(default=512, description="Document chunk size")
    CHUNK_OVERLAP: int = Field(default=50, description="Document chunk overlap")
    SIMILARITY_THRESHOLD: float = Field(default=0.7, description="Similarity search threshold")
    MAX_SEARCH_RESULTS: int = Field(default=10, description="Maximum search results")
    ENABLE_HYBRID_SEARCH: bool = Field(default=True, description="Enable hybrid search")
    CACHE_EMBEDDINGS: bool = Field(default=True, description="Cache embeddings")

    # =====================================================================================
    # APPROVAL SYSTEM CONFIGURATION
    # =====================================================================================

    APPROVAL_ENABLED: bool = Field(default=True, description="Enable approval system")
    AUTO_APPROVE_LOW_RISK: bool = Field(default=False, description="Auto-approve low-risk operations")
    APPROVAL_TIMEOUT_MINUTES: int = Field(default=60, description="Approval timeout in minutes")

    # =====================================================================================
    # WORKSPACE & PROJECT CONFIGURATION
    # =====================================================================================

    WORKSPACE_PATH: Path = Field(
        default=Path("/home/<USER>/workspace"),
        description="Base workspace directory path"
    )
    CODE_SERVER_URL: AnyHttpUrl = Field(
        default="http://localhost:8080",
        description="Code server URL"
    )

    @validator('WORKSPACE_PATH', pre=True)
    def parse_workspace_path(cls, v: Union[str, Path]) -> Path:
        """Parse workspace path from string or Path object."""
        return Path(v)

    # =====================================================================================
    # MONITORING & LOGGING CONFIGURATION
    # =====================================================================================

    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    LOG_FORMAT: str = Field(
        default="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        description="Log format string"
    )
    ENABLE_METRICS: bool = Field(default=True, description="Enable Prometheus metrics")
    METRICS_PORT: int = Field(default=9090, description="Metrics server port")

    # Health Check Configuration
    HEALTH_CHECK_INTERVAL: int = Field(default=30, description="Health check interval in seconds")
    HEALTH_CHECK_TIMEOUT: int = Field(default=10, description="Health check timeout in seconds")

    # Rate Limiting
    RATE_LIMIT_ENABLED: bool = Field(default=True, description="Enable rate limiting")
    RATE_LIMIT_REQUESTS: int = Field(default=100, description="Rate limit requests per window")
    RATE_LIMIT_WINDOW: int = Field(default=60, description="Rate limit window in seconds")

    # =====================================================================================
    # DOCKER & CONTAINER CONFIGURATION
    # =====================================================================================

    DOCKER_SOCKET: str = Field(
        default="/var/run/docker.sock",
        description="Docker socket path"
    )
    CONTAINER_MEMORY_LIMIT: str = Field(
        default="2g",
        description="Default container memory limit"
    )
    CONTAINER_CPU_LIMIT: str = Field(
        default="1.0",
        description="Default container CPU limit"
    )

    # =====================================================================================
    # CONFIGURATION VALIDATION & COMPUTED PROPERTIES
    # =====================================================================================

    @validator('JWT_SECRET')
    def validate_jwt_secret(cls, v: str) -> str:
        """Validate JWT secret strength."""
        if len(v) < 32:
            raise ValueError("JWT_SECRET must be at least 32 characters long")
        return v

    @property
    def redis_available(self) -> bool:
        """Check if Redis is configured and available."""
        return bool(self.REDIS_URL)

    @property
    def supabase_available(self) -> bool:
        """Check if Supabase is properly configured."""
        if not self.USE_SUPABASE:
            return False
        return all([
            self.SUPABASE_URL,
            self.SUPABASE_ANON_KEY,
            self.SUPABASE_SERVICE_KEY
        ])

    @property
    def openrouter_available(self) -> bool:
        """Check if OpenRouter is configured."""
        return bool(self.OPENROUTER_API_KEY)

    @property
    def openai_available(self) -> bool:
        """Check if OpenAI is configured."""
        return bool(self.OPENAI_API_KEY)

    @property
    def anthropic_available(self) -> bool:
        """Check if Anthropic is configured."""
        return bool(self.ANTHROPIC_API_KEY)

    @property
    def github_oauth_available(self) -> bool:
        """Check if GitHub OAuth is configured."""
        return bool(self.GITHUB_CLIENT_ID and self.GITHUB_CLIENT_SECRET)

    # =====================================================================================
    # PYDANTIC SETTINGS CONFIGURATION
    # =====================================================================================

    class Config:
        """Pydantic settings configuration (v2-compatible)."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = True
        validate_assignment = True
        extra = "forbid"

        # Field customization (rename schema_extra -> json_schema_extra for Pydantic v2)
        json_schema_extra = {
            "example": {
                "APP_NAME": "AI Coding Agent Orchestrator",
                "ENVIRONMENT": "development",
                "DATABASE_URL": "postgresql+asyncpg://user:pass@localhost:5432/ai_coding_agent",
                "REDIS_URL": "redis://localhost:6379/0",
                "JWT_SECRET": "your-super-secret-jwt-key-here-make-it-long-and-secure"
            }
        }


# =====================================================================================
# GLOBAL SETTINGS INSTANCE
# =====================================================================================

# Create global settings instance (singleton pattern)
settings = Settings()


# =====================================================================================
# CONFIGURATION UTILITIES
# =====================================================================================

def get_settings() -> Settings:
    """
    Get the global settings instance.

    This function provides dependency injection for FastAPI endpoints
    and ensures a singleton pattern for the settings.

    Returns:
        Settings: The global settings instance
    """
    return settings


def reload_settings() -> Settings:
    """
    Reload settings from environment variables.

    Useful for testing or when environment variables change.

    Returns:
        Settings: New settings instance with current environment
    """
    global settings
    settings = Settings()
    return settings


def validate_configuration() -> bool:
    """
    Validate the current configuration.

    Performs comprehensive validation of all settings and
    checks for required dependencies.

    Returns:
        bool: True if configuration is valid

    Raises:
        ValueError: If configuration is invalid
    """
    try:
        # Validate core settings
        if not settings.JWT_SECRET:
            raise ValueError("JWT_SECRET is required")

        if not settings.DATABASE_URL:
            raise ValueError("DATABASE_URL is required")

        if not settings.REDIS_URL:
            raise ValueError("REDIS_URL is required")

        # Validate Supabase if enabled
        if settings.USE_SUPABASE and not settings.supabase_available:
            raise ValueError("Supabase configuration incomplete")

        # Validate paths
        if not settings.WORKSPACE_PATH.is_absolute():
            raise ValueError("WORKSPACE_PATH must be an absolute path")

        return True

    except Exception as e:
        raise ValueError(f"Configuration validation failed: {str(e)}")


def get_llm_provider_config(provider: LLMProvider) -> dict:
    """
    Get configuration for a specific LLM provider.

    Args:
        provider: LLM provider to get configuration for

    Returns:
        dict: Provider configuration
    """
    provider_configs = {
        LLMProvider.OLLAMA: {
            "base_url": str(settings.OLLAMA_BASE_URL),
            "model": settings.OLLAMA_MODEL,
            "timeout": settings.OLLAMA_TIMEOUT,
            "available": True  # Ollama doesn't require API key
        },
        LLMProvider.OPENROUTER: {
            "base_url": str(settings.OPENROUTER_BASE_URL),
            "model": settings.OPENROUTER_MODEL,
            "api_key": settings.OPENROUTER_API_KEY,
            "available": settings.openrouter_available
        },
        LLMProvider.OPENAI: {
            "base_url": str(settings.OPENAI_BASE_URL),
            "model": settings.OPENAI_MODEL,
            "api_key": settings.OPENAI_API_KEY,
            "available": settings.openai_available
        },
        LLMProvider.ANTHROPIC: {
            "base_url": str(settings.ANTHROPIC_BASE_URL),
            "model": settings.ANTHROPIC_MODEL,
            "api_key": settings.ANTHROPIC_API_KEY,
            "available": settings.anthropic_available
        }
    }

    return provider_configs.get(provider, {})


def get_embedding_config() -> dict:
    """
    Get embedding configuration.

    Returns:
        dict: Embedding configuration
    """
    return {
        "provider": settings.EMBEDDING_PROVIDER,
        "model": settings.EMBEDDING_MODEL,
        "dimension": settings.EMBEDDING_DIMENSION,
        "chunk_size": settings.CHUNK_SIZE,
        "chunk_overlap": settings.CHUNK_OVERLAP,
        "similarity_threshold": settings.SIMILARITY_THRESHOLD,
        "max_search_results": settings.MAX_SEARCH_RESULTS,
        "enable_hybrid_search": settings.ENABLE_HYBRID_SEARCH,
        "cache_embeddings": settings.CACHE_EMBEDDINGS
    }